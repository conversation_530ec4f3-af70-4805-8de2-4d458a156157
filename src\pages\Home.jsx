import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { MessageSquare, Shield, Zap, Users, TrendingUp, CheckCircle } from 'lucide-react';
import logo from '../assets/logo.png';

const Home = () => {
  const { isAuthenticated, user } = useAuth();

  const features = [
    {
      icon: MessageSquare,
      title: 'Intelligent Content Analysis',
      description: 'Advanced AI algorithms detect toxicity, sentiment, and harmful content with precision.',
    },
    {
      icon: Shield,
      title: 'Sentinel Dashboard',
      description: 'Comprehensive command center for monitoring, reviewing, and managing all content decisions.',
    },
    {
      icon: Zap,
      title: 'Lightning-Fast Processing',
      description: 'Real-time content analysis and instant moderation decisions at scale.',
    },
    {
      icon: Users,
      title: 'Multi-Level Security',
      description: 'Role-based access control with granular permissions for different user types.',
    },
  ];

  const stats = [
    { label: 'Content Analyzed', value: '1M+', icon: MessageSquare },
    { label: 'Detection Accuracy', value: '98.5%', icon: TrendingUp },
    { label: 'Protected Communities', value: '1,200+', icon: Users },
    { label: 'System Reliability', value: '99.9%', icon: CheckCircle },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="text-center py-20">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center mb-8 group">
            <div className="w-16 h-16 sentinel-logo rounded-xl flex items-center justify-center mr-4 p-2">
              <img
                src={logo}
                alt="SENTINEL AI Logo"
                className="w-full h-full object-contain"
              />
            </div>
            <h1 className="text-5xl md:text-6xl font-bold sentinel-text animate-fadeIn tracking-wide group-hover:scale-105 transition-transform duration-300">
              SENTINEL AI
            </h1>
          </div>

          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 animate-fadeIn">
            Advanced Content
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              {' '}Moderation
            </span>
          </h2>

          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto animate-fadeIn">
            Protect your digital community with SENTINEL AI's intelligent content filtering,
            real-time sentiment analysis, and automated moderation powered by cutting-edge AI technology.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fadeIn">
            {!isAuthenticated ? (
              <>
                <Link to="/register" className="btn-primary text-lg px-8 py-3">
                  Get Started
                </Link>
                <Link 
                  to="/login" 
                  className="btn-secondary text-lg px-8 py-3"
                >
                  Sign In
                </Link>
              </>
            ) : (
              <div className="flex flex-col sm:flex-row gap-4">
                {user?.role !== 'admin' && (
                  <Link to="/comments" className="btn-primary text-lg px-8 py-3">
                    View Comments
                  </Link>
                )}
                {user?.role === 'admin' && (
                  <Link to="/admin" className="btn-secondary text-lg px-8 py-3">
                    Admin Panel
                  </Link>
                )}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="glass rounded-lg p-6 text-center animate-fadeIn">
                <stat.icon className="w-8 h-8 text-blue-400 mx-auto mb-4" />
                <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                <div className="text-gray-400">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Advanced AI Capabilities
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              SENTINEL AI provides comprehensive protection for your digital community with state-of-the-art technology
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="glass rounded-lg p-6 hover:bg-opacity-20 transition-all duration-300 animate-fadeIn">
                <feature.icon className="w-12 h-12 text-blue-400 mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-300">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      {!isAuthenticated && (
        <section className="py-20">
          <div className="max-w-4xl mx-auto text-center">
            <div className="glass rounded-2xl p-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Deploy SENTINEL AI Today
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                Join thousands of communities protected by SENTINEL AI's advanced content moderation
              </p>
              <Link to="/register" className="btn-primary text-lg px-8 py-3">
                Create Your Account
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* Welcome Back Section for Authenticated Users */}
      {isAuthenticated && (
        <section className="py-20">
          <div className="max-w-4xl mx-auto text-center">
            <div className="glass rounded-2xl p-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Welcome back, {user?.name}!
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                {user?.role === 'admin'
                  ? 'Access SENTINEL AI\'s command center to monitor and protect your community'
                  : 'Your content is protected by SENTINEL AI\'s advanced moderation system'
                }
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                {user?.role !== 'admin' && (
                  <Link to="/comments" className="btn-primary text-lg px-8 py-3">
                    View Comments
                  </Link>
                )}
                {user?.role === 'admin' && (
                  <Link to="/admin" className="btn-secondary text-lg px-8 py-3">
                    SENTINEL Command Center
                  </Link>
                )}
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
};

export default Home;
